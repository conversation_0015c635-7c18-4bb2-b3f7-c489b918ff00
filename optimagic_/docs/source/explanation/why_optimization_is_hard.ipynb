{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Why optimization is difficult\n", "\n", "This tutorial shows why optimization is difficult and why you need some knowledge in order to solve optimization problems efficiently. It is meant for people who have no previous experience with numerical optimization and wonder why there are so many optimization algorithms and still none that works for all problems. For each potential problem we highlight, we also give some ideas on how to solve it. \n", "\n", "\n", "If you simply want to learn the mechanics of doing optimization with optimagic, check out the [quickstart guide](../tutorials/optimization_overview.ipynb)\n", "\n", "\n", "The take-home message of this notebook can be summarized as follows:\n", "\n", "- The only algorithms that are guaranteed to solve all problems are grid search or other algorithms that evaluate the criterion function almost everywhere in the parameter space.\n", "- If you have more than a hand full of parameters, these methods would take too long.\n", "- Thus, you have to know the properties of your optimization problem and have knowledge about different optimization algorithms in order to choose the right algorithm for your problem. \n", "\n", "This tutorial uses variants of the sphere function from the [quickstart guide](../tutorials/optimization_overview.ipynb)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import seaborn as sns\n", "\n", "import optimagic as om"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def sphere(x):\n", "    return x @ x\n", "\n", "\n", "def sphere_gradient(x):\n", "    return 2 * x"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Why grid search is infeasible\n", "\n", "Sampling based optimizers and grid search require the parameter space to be bounded in all directions. Let's assume we know that the optimum of the sphere function lies between -0.5 and 0.5, but don't know where it is exactly. \n", "\n", "In order to get a precision of 2 digits with grid search, we require the following number of function evaluations (depending on the number of parameters):"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dimensions = np.arange(10) + 1\n", "n_evals = 100**dimensions\n", "sns.lineplot(x=dimensions, y=n_evals);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you have 10 dimensions and evaluating your criterion function takes one second, you need about 3 billion years on a 1000 core cluster. Many of the real world criterion functions have hundreds of parameters and take minutes to evaluate once. This is called the curse of dimensionality."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Sampling based algorithms typically fix the number of criterion evaluations and apply them a bit smarter than algorithms that rummage the search space randomly. However, these smart tricks only work under additional assumptions. Thus, either you need to make assumptions on your problem or you will get the curse of dimensionality through the backdoor again. For easier analysis, assume we fix the number of function evaluations in a grid search instead of a sampling based algorithm and want to know which precision we can get, depending on the dimension:\n", "\n", "For 1 million function evaluations, we can expect the following precision:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dimensions = np.arange(10) + 1\n", "precision = 1e-6 ** (1 / dimensions)\n", "sns.lineplot(x=dimensions, y=precision);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## How derivatives can solve the curse of dimensionality\n", "\n", "Derivative based methods do not try to evaluate the criterion function everywhere in the search space. Instead, they start at some point and go \"downhill\" from there. The gradient of the criterion function indicates which direction is downhill. Then there are different ways of determining how far to go in that direction. The time it takes to evaluate a derivative increases at most linearly in the number of parameters. Using the derivative information, optimizers can often find an optimum with very few function evaluations."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## How derivative based methods can fail\n", "\n", "To see how derivative based methods can fail, we use simple modifications of the sphere function. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rng = np.random.default_rng(seed=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def sphere_with_noise(x, rng):\n", "    return sphere(x) + rng.normal(scale=0.02)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_params = np.arange(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grid = np.linspace(-1, 1, 1000)\n", "sns.lineplot(\n", "    x=grid,\n", "    y=(grid**2) + rng.normal(scale=0.02, size=len(grid)),\n", ");"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res = om.minimize(\n", "    fun=sphere_with_noise,\n", "    params=start_params,\n", "    algorithm=\"scipy_lbfgsb\",\n", "    logging=False,\n", "    fun_kwargs={\"rng\": rng},\n", ")\n", "\n", "res.success"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res.params"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res.message"]}, {"cell_type": "markdown", "metadata": {}, "source": ["So the algorithm failed, but at least tells you that it did not succed. Let's look at a different kind of numerical noise that could come from rounding. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def piecewise_constant_sphere(x):\n", "    return sphere(x.round(2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sns.lineplot(x=grid, y=grid.round(2) ** 2);"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res = om.minimize(\n", "    fun=piecewise_constant_sphere,\n", "    params=start_params,\n", "    algorithm=\"scipy_lbfgsb\",\n", ")\n", "\n", "res"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This time, the algorithm failed silently."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 4}