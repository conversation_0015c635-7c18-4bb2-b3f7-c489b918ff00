{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["# How to visualize optimizer histories\n", "\n", "optimagic's `criterion_plot` can visualize the history of function values for one or multiple optimizations. \n", "optimagic's `params_plot` can visualize the history of parameter values for one optimization. \n", "\n", "This can help you to understand whether your optimization actually converged and if not, which parameters are problematic. \n", "\n", "It can also help you to find the fastest optimizer for a given optimization problem. "]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import plotly.io as pio\n", "\n", "pio.renderers.default = \"notebook_connected\"\n", "\n", "import optimagic as om"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["## Run two optimization to get example results"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["def sphere(x):\n", "    return x @ x\n", "\n", "\n", "results = {}\n", "for algo in [\"scipy_lbfgsb\", \"scipy_neldermead\"]:\n", "    results[algo] = om.minimize(sphere, params=np.arange(5), algorithm=algo)"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["## Make a single criterion plot"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["fig = om.criterion_plot(results[\"scipy_neldermead\"])\n", "fig.show()"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["## Compare two optimizations in a criterion plot"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["fig = om.criterion_plot(results)\n", "fig.show()"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["## Use some advanced options of criterion_plot"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["fig = om.criterion_plot(\n", "    results,\n", "    # cut off after 180 evaluations\n", "    max_evaluations=180,\n", "    # show only the current best function value\n", "    monotone=True,\n", ")\n", "fig.show()"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["## Make a params plot"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["fig = om.params_plot(results[\"scipy_neldermead\"])\n", "fig.show()"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["## Use advanced options of params plot"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["fig = om.params_plot(\n", "    results[\"scipy_neldermead\"],\n", "    # cut off after 180 evaluations\n", "    max_evaluations=180,\n", "    # select only the last three parameters\n", "    selector=lambda x: x[2:],\n", ")\n", "fig.show()"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["## criterion_plot with multistart optimization"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["def alpine(x):\n", "    return np.sum(np.abs(x * np.sin(x) + 0.1 * x))\n", "\n", "\n", "res = om.minimize(\n", "    alpine,\n", "    params=np.arange(7),\n", "    bounds=om.Bounds(soft_lower=np.full(7, -3), soft_upper=np.full(7, 10)),\n", "    algorithm=\"scipy_neldermead\",\n", "    multistart=om.MultistartOptions(n_samples=100, convergence_max_discoveries=3),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["fig = om.criterion_plot(res, max_evaluations=1000, monotone=True)\n", "fig.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}