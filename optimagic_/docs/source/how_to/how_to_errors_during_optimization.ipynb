{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["(how-to-errors)=\n", "\n", "# How to handle errors during optimization\n", "\n", "## Try to avoid errors\n", "\n", "Often, optimizers try quite extreme parameter vectors, which then can raise errors in your criterion function or derivative. Often, there are simple tricks to make your code more robust. Avoiding errors is always better than dealing with errors after they occur.  \n", "\n", "- Avoid to take ``np.exp`` without further safeguards. With 64 bit floating point numbers, the exponential function is only well defined roughly between -700 and 700. Below it is 0, above it is inf. Sometimes you can use ``scipy.special.logsumexp`` to avoid unsafe evaluations of the exponential. Read [this](https://en.wikipedia.org/wiki/LogSumExp) for background information on the logsumexp trick.\n", "- Set bounds for your parameters that prevent extreme parameter constellations.\n", "- Use the ``bounds_distance`` option with a not too small value for ``covariance`` and ``sdcorr`` constraints.\n", "- Use `optimagic.utilities.robust_cholesky` instead of normal\n", "  cholesky decompositions or try to avoid cholesky decompositions.\n", "- Use a less aggressive optimizer. Trust region optimizers like `fides` usually choose less extreme steps in the beginnig than line search optimizers like `scipy_bfgs` and `scip_lbfgsb`. \n", "\n", "## Do not use clipping\n", "\n", "A commonly chosen solution to numerical problems is clipping of extreme values. Naive clipping leads to flat areas in your criterion function and can cause spurious convergence. Only use clipping if you know that your optimizer can deal with flat parts. "]}, {"cell_type": "markdown", "id": "1", "metadata": {}, "source": ["## Let optimagic do its magic\n", "\n", "Instead of avoiding errors in your criterion function, you can raise them and let optimagic deal with them. If you are using numerical derivatives, errors will automatically be raised if any entry in the derivative is not finite. \n", "\n", "### An example\n", "\n", "Let's look at a simple example from the Moré-Wild benchmark set that has a numerical instability. "]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "import numpy as np\n", "import plotly.io as pio\n", "from scipy.optimize import minimize as scipy_minimize\n", "\n", "pio.renderers.default = \"notebook_connected\"\n", "\n", "import optimagic as om\n", "\n", "warnings.simplefilter(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["def jenn<PERSON>_sampson(x):\n", "    dim_out = 10\n", "    fvec = (\n", "        2 * (1.0 + np.arange(1, dim_out + 1))\n", "        - np.exp(np.arange(1, dim_out + 1) * x[0])\n", "        - np.exp(np.arange(1, dim_out + 1) * x[1])\n", "    )\n", "    return fvec @ fvec\n", "\n", "\n", "correct_params = np.array([0.2578252135686162, 0.2578252135686162])\n", "correct_criterion = 124.3621823556148\n", "\n", "start_x = np.array([0.3, 0.4])"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["### What would scipy do?"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["scipy_res = scipy_minimize(jen<PERSON><PERSON>_<PERSON>on, x0=start_x, method=\"L-BFGS-B\")"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["scipy_res.success"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["correct_params.round(4), scipy_res.x.round(4)"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["So, scipy thinks it solved the problem successfully but the result is far off. (Note that scipy would have given us a warning, but we disabled warnings in order to not clutter the output).\n", "\n", "### optimagic's error handling magic"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["res = om.minimize(\n", "    fun=jenn<PERSON>_sampson,\n", "    params=start_x,\n", "    algorithm=\"scipy_lbfgsb\",\n", "    error_handling=\"continue\",\n", ")\n", "\n", "correct_params, res.params"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["### How does the magic work\n", "\n", "When an error occurs and `error_handling` is set to `\"continue\"`, optimagic replaces your criterion with a dummy function (and adjusts the derivative accordingly). \n", "\n", "The dummy function has two important properties:\n", "\n", "1. Its value is always higher than criterion at start params. \n", "2. Its slope guides the optimizer back towards the start parameters. I.e., if you are minimizing, the direction of strongest decrease is towards the start parameters; if you are maximizing, the direction of strongest increase is towards the start parameters. \n", "\n", "Therefore, when hitting an undefined area, an optimizer can take a few steps back until it is in better territory and then continue its work. \n", "\n", "Importantly, the optimizer will not simply go back to a previously evaluated point (which would just lead to cyclical behavior). It will just go back in the direction it originally came from.\n", "\n", "In the concrete example, the dummy function would look similar to the following:"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["def dummy(params):\n", "    start_params = np.array([0.3, 0.4])\n", "    # this is close to the actual value used by optimagic\n", "    constant = 8000\n", "    # the actual slope used by optimagic would be even smaller\n", "    slope = 10_000\n", "    diff = params - start_params\n", "    return constant + slope * np.linalg.norm(diff)"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["Now, let's plot the two functions. For better illustration, we assume that the jen<PERSON><PERSON>_<PERSON>mpson function is only defined until it reaches a value of 100_000 and the dummy function takes over from there.  "]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["from plotly import graph_objects as go\n", "\n", "grid = np.linspace(0, 1)\n", "params = [np.full(2, val) for val in grid]\n", "values = np.array([jen<PERSON><PERSON>_sampson(p) for p in params])\n", "values = np.where(values <= 1e5, values, np.nan)\n", "dummy_values = np.array([dummy(p) for p in params])\n", "dummy_values = np.where(np.isfinite(values), np.nan, dummy_values)"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["fig = go.Figure()\n", "fig.add_trace(go.<PERSON><PERSON>(x=grid, y=values))\n", "fig.add_trace(go.<PERSON><PERSON>(x=grid, y=dummy_values))\n", "fig.show()"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["We can see that the dummy function is lower than the highest achieved value of `jenn<PERSON>_sampson` but higher than the start values. It is also rather flat. Fortunately, that is all we need. "]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}