# GeneticAlgorithmPython

This folder has the code for the tutorial titled [**Genetic Algorithm Implementation in Python**](https://www.linkedin.com/pulse/genetic-algorithm-implementation-python-ahmed-gad) which is available at these links:

* https://www.linkedin.com/pulse/genetic-algorithm-implementation-python-ahmed-gad
* https://towardsdatascience.com/genetic-algorithm-implementation-in-python-5ab67bb124a6
* https://www.kdnuggets.com/2018/07/genetic-algorithm-implementation-python.html

The `ga.py` file has the implementation of the GA operations such as mutation and crossover. It is a primitive implementation of the genetic algorithm. The other file gives an example of using the ga.py file.

It is important to note that this project does not implement everything in GA and there are a wide number of variations to be applied. For example, this project uses decimal representation for the chromosome and the binary representations might be preferred for other problems. Check [PyGAD](https://pygad.readthedocs.io/en) for extensive features.
